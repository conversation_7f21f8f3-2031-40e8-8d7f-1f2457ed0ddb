{"name": "auspex-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit", "build:staging": "NEXT_PUBLIC_API_URL=https://ma1l0omu6k.execute-api.us-west-1.amazonaws.com npm run build", "build:prod": "NEXT_PUBLIC_API_URL=https://e4n0a6vbud.execute-api.us-west-1.amazonaws.com npm run build", "deploy:staging": "npm run build:staging && cd terraform/environments/staging && aws s3 sync ../../../out/ s3://$(terraform output -raw s3_bucket_name) --delete && aws cloudfront create-invalidation --distribution-id $(terraform output -raw cloudfront_distribution_id) --paths '/*'", "deploy:prod": "npm run build:prod && cd terraform/environments/prod && aws s3 sync ../../../out/ s3://$(terraform output -raw s3_bucket_name) --delete && aws cloudfront create-invalidation --distribution-id $(terraform output -raw cloudfront_distribution_id) --paths '/*'"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^11.3.12", "lucide-react": "^0.475.0", "next": "^14.2.32", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.5", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}